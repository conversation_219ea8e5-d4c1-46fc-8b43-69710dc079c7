using AutoFixture;
using AutoFixture.AutoMoq;
using AutoMapper;
using FluentAssertions;
using GlobalTrader2.Aggregator.UseCases.Orders.Requirements.ExportToCSV.Commands;
using GlobalTrader2.Aggregator.UseCases.Orders.Requirements.ExportToCSV.Commands.Dtos;
using GlobalTrader2.Core;
using GlobalTrader2.Core.Domain.Entities;
using GlobalTrader2.Core.Enums;
using Moq;
using System.Reflection;
using System.Text;

namespace GlobalTrader2.Aggregator.Test.Orders.Requirements.ExportToCSV.Commands
{
    public class CreateExportToCSVHandlerTests
    {
        private readonly IFixture _fixture;
        private readonly Mock<IBaseRepository<CustomerRequirementForBom>> _mockRepository;
        private readonly Mock<IBaseRepository<PurchaseRequestLineDetail>> _mockRepositoryPurchaseRequestLine;
        private readonly Mock<IBaseRepository<ReportColumn>> _mockRepositoryReportColumn;
        private readonly Mock<IMapper> _mockMapper;
        private readonly CreateExportToCsvHandler _handler;

        public CreateExportToCSVHandlerTests()
        {
            _fixture = new Fixture().Customize(new AutoMoqCustomization());
            _fixture.Behaviors.Add(new OmitOnRecursionBehavior());

            _mockRepository = _fixture.Freeze<Mock<IBaseRepository<CustomerRequirementForBom>>>();
            _mockRepositoryPurchaseRequestLine = _fixture.Freeze<Mock<IBaseRepository<PurchaseRequestLineDetail>>>();
            _mockRepositoryReportColumn = _fixture.Freeze<Mock<IBaseRepository<ReportColumn>>>();
            _mockMapper = _fixture.Freeze<Mock<IMapper>>();

            _handler = new CreateExportToCsvHandler(
                _mockRepository.Object,
                _mockRepositoryReportColumn.Object,
                _mockRepositoryPurchaseRequestLine.Object,
                _mockMapper.Object);
        }

        [Fact]
        public async Task Handle_WhenRequestIsNull_ShouldThrowArgumentNullException()
        {
            // Arrange
            CreateExportToCsvCommand? request = null;

            // Act & Assert
            await Assert.ThrowsAsync<ArgumentNullException>(() =>
                _handler.Handle(request!, CancellationToken.None));
        }

        [Fact]
        public async Task Handle_WhenReportIsRequirementWithBOM_ShouldReturnSuccessWithCorrectFileName()
        {
            // Arrange
            var request = CreateTestCommand(Report.RequirementWithBOM, "N");
            var mockBomData = CreateMockBomData();
            var mockColumns = CreateMockReportColumns();

            SetupMockRepositories(mockBomData, null, mockColumns);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            result.Success.Should().BeTrue();
            result.Data.Should().NotBeNull();
            result.Data.FileName.Should().StartWith("PriceRequest_");
            result.Data.FileName.Should().EndWith(".csv");
            result.Data.File.Should().NotBeEmpty();
        }

        [Fact]
        public async Task Handle_WhenReportIsPurchaseQuote_ShouldReturnSuccessWithCorrectFileName()
        {
            // Arrange
            var request = CreateTestCommand(Report.PurchaseQuote, "N");
            var mockPurchaseData = CreateMockPurchaseRequestLineData();
            var mockColumns = CreateMockReportColumns();

            SetupMockRepositories(null, mockPurchaseData, mockColumns);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            result.Success.Should().BeTrue();
            result.Data.Should().NotBeNull();
            result.Data.FileName.Should().StartWith("PriceRequest_");
            result.Data.FileName.Should().EndWith(".csv");
            result.Data.File.Should().NotBeEmpty();
        }

        [Fact]
        public async Task Handle_WhenExportIsE_ShouldGenerateFileNameWithE()
        {
            // Arrange
            var request = CreateTestCommand(Report.RequirementWithBOM, "E");
            var mockBomData = CreateMockBomData();
            var mockColumns = CreateMockReportColumns();

            SetupMockRepositories(mockBomData, null, mockColumns);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            result.Success.Should().BeTrue();
            result.Data.FileName.Should().StartWith("PriceRequestE_");
        }

        [Fact]
        public async Task Handle_WhenExportIsNotE_ShouldGenerateFileNameWithoutE()
        {
            // Arrange
            var request = CreateTestCommand(Report.RequirementWithBOM, "N");
            var mockBomData = CreateMockBomData();
            var mockColumns = CreateMockReportColumns();

            SetupMockRepositories(mockBomData, null, mockColumns);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            result.Success.Should().BeTrue();
            result.Data.FileName.Should().StartWith("PriceRequest_");
            result.Data.FileName.Should().NotContain("PriceRequestE_");
        }

        [Fact]
        public async Task Handle_WhenDataExists_ShouldGenerateCSVWithCorrectFormat()
        {
            // Arrange
            var request = CreateTestCommand(Report.RequirementWithBOM, "N");
            var mockBomData = CreateMockBomData();
            var mockColumns = CreateMockReportColumns();

            SetupMockRepositories(mockBomData, null, mockColumns);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            result.Success.Should().BeTrue();
            var csvContent = Encoding.UTF8.GetString(result.Data.File);
            csvContent.Should().NotBeEmpty();
            csvContent.Should().Contain("RequirementWithBOM"); // Report title
            csvContent.Should().Contain("Abbreviation->"); // Abbreviation line
        }

        [Fact]
        public void ConverListObject_WhenSourceListIsNull_ShouldReturnEmptyList()
        {
            // Arrange
            List<CustomerRequirementForBomDto>? sourceList = null;

            // Act
            var result = CreateExportToCsvHandler.ConverListObject(sourceList!);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Fact]
        public void ConverListObject_WhenSourceListIsEmpty_ShouldReturnEmptyList()
        {
            // Arrange
            var sourceList = new List<CustomerRequirementForBomDto>();

            // Act
            var result = CreateExportToCsvHandler.ConverListObject(sourceList);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Fact]
        public void ConverListObject_WhenSourceListHasData_ShouldConvertToObjectList()
        {
            // Arrange
            var sourceList = new List<CustomerRequirementForBomDto>
            {
                new() { RequirementNumber = "REQ001", Company = 1, QuantityQuoted = 100 },
                new() { RequirementNumber = "REQ002", Company = 2, QuantityQuoted = 200 }
            };

            // Act
            var result = CreateExportToCsvHandler.ConverListObject(sourceList);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            result[0].Should().NotBeEmpty();
            result[1].Should().NotBeEmpty();
        }

        [Fact]
        public void ConverListObject_WhenPropertyIsNull_ShouldUseEmptyBraces()
        {
            // Arrange
            var sourceList = new List<CustomerRequirementForBomDto>
            {
                new() { RequirementNumber = null, Company = 1, QuantityQuoted = 100 }
            };

            // Act
            var result = CreateExportToCsvHandler.ConverListObject(sourceList);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(1);
            result[0].Should().Contain("{}");
        }

        [Fact]
        public async Task Handle_WhenDataHasMoreThan13Columns_ShouldIncludeNotesInHeadings()
        {
            // Arrange
            var request = CreateTestCommand(Report.RequirementWithBOM, "N");
            var mockBomData = CreateMockBomDataWithManyColumns();
            var mockColumns = CreateMockReportColumns();

            SetupMockRepositories(mockBomData, null, mockColumns);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            result.Success.Should().BeTrue();
            var csvContent = Encoding.UTF8.GetString(result.Data.File);
            csvContent.Should().Contain("Notes:");
        }

        [Fact]
        public async Task Handle_WhenPurchaseQuoteWithExportE_ShouldGenerateCorrectFileName()
        {
            // Arrange
            var request = CreateTestCommand(Report.PurchaseQuote, "E");
            var mockPurchaseData = CreateMockPurchaseRequestLineData();
            var mockColumns = CreateMockReportColumns();

            SetupMockRepositories(null, mockPurchaseData, mockColumns);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            result.Success.Should().BeTrue();
            result.Data.FileName.Should().StartWith("PriceRequestE_");
            result.Data.FileName.Should().EndWith(".csv");
        }

        [Fact]
        public async Task Handle_WhenResourcesAreEmpty_ShouldUseResourceKeys()
        {
            // Arrange
            var request = CreateTestCommand(Report.RequirementWithBOM, "N");
            request.Resources = new List<(string key, string value)>(); // Empty resources
            var mockBomData = CreateMockBomData();
            var mockColumns = CreateMockReportColumns();

            SetupMockRepositories(mockBomData, null, mockColumns);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            result.Success.Should().BeTrue();
            var csvContent = Encoding.UTF8.GetString(result.Data.File);
            csvContent.Should().NotBeEmpty();
        }

        [Fact]
        public async Task Handle_WhenRepositoryThrowsException_ShouldPropagateException()
        {
            // Arrange
            var request = CreateTestCommand(Report.RequirementWithBOM, "N");

            _mockRepository.Setup(x => x.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                .ThrowsAsync(new InvalidOperationException("Database error"));

            // Act & Assert
            await Assert.ThrowsAsync<InvalidOperationException>(() =>
                _handler.Handle(request, CancellationToken.None));
        }

        [Fact]
        public async Task Handle_WhenMapperThrowsException_ShouldPropagateException()
        {
            // Arrange
            var request = CreateTestCommand(Report.RequirementWithBOM, "N");

            _mockRepository.Setup(x => x.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                .ReturnsAsync(new List<CustomerRequirementForBom>());
            _mockMapper.Setup(x => x.Map<List<CustomerRequirementForBomDto>>(It.IsAny<object>()))
                .Throws(new AutoMapperMappingException("Mapping error"));

            // Act & Assert
            await Assert.ThrowsAsync<AutoMapperMappingException>(() =>
                _handler.Handle(request, CancellationToken.None));
        }

        // Helper methods for creating test data
        private CreateExportToCsvCommand CreateTestCommand(Report report, string export)
        {
            return new CreateExportToCsvCommand
            {
                Report = report,
                Id = 123,
                CurrencyCode = "USD",
                Export = export,
                ClientID = 456,
                LoginFullName = "Test User",
                Resources = new List<(string key, string value)>
                {
                    ("ReportTitlesRequirementWithBOM", "RequirementWithBOM"),
                    ("ReportTitlesPurchaseQuote", "PurchaseQuote"),
                    ("MiscAppTitle", "GlobalTrader"),
                    ("ReportsDateAndLogin", "Date: {0}, Login: {1}"),
                    ("MiscNotes", "Notes: "),
                    ("NotFoundReportData", "No data found"),
                    ("ReportsTestColumn1", "TestColumn1"),
                    ("ReportsTestColumn2", "TestColumn2"),
                    ("ReportsUnitPrice", "UnitPrice")
                }
            };
        }

        private List<CustomerRequirementForBomDto> CreateMockBomData()
        {
            return new List<CustomerRequirementForBomDto>
            {
                new()
                {
                    RequirementNumber = "REQ001",
                    Company = 1,
                    QuantityQuoted = 100,
                    MPNQuoted = "TEST-PART-001",
                    ManufacturerName = "Test Manufacturer",
                    UnitPrice = 10.50m
                }
            };
        }

        private List<CustomerRequirementForBomDto> CreateMultipleMockBomData()
        {
            return new List<CustomerRequirementForBomDto>
            {
                new()
                {
                    RequirementNumber = "REQ001",
                    Company = 1,
                    QuantityQuoted = 100,
                    MPNQuoted = "TEST-PART-001",
                    ManufacturerName = "Test Manufacturer",
                    UnitPrice = 10.50m
                },
                new()
                {
                    RequirementNumber = "REQ002",
                    Company = 2,
                    QuantityQuoted = 200,
                    MPNQuoted = "TEST-PART-002",
                    ManufacturerName = "Test Manufacturer 2",
                    UnitPrice = 20.75m
                }
            };
        }

        private List<CustomerRequirementForBomDto> CreateMockBomDataWithManyColumns()
        {
            return new List<CustomerRequirementForBomDto>
            {
                new()
                {
                    RequirementNumber = "REQ001",
                    Company = 1,
                    QuantityQuoted = 100,
                    MPNQuoted = "TEST-PART-001",
                    ManufacturerName = "Test Manufacturer",
                    DateCode = "2024",
                    PackageType = "SOIC",
                    ProductType = "IC",
                    SPQ = "1000",
                    MOQ = "100",
                    LeadTimeWks = "12",
                    RohsYN = "Y",
                    TQSA = "5000",
                    LTB = "N",
                    FactorySealed = "Y",
                    MSL = "3",
                    UnitPrice = 10.50m,
                    SupplierNotes = "Test notes for column 15+"
                }
            };
        }

        private List<PurchaseRequestLineDetailDto> CreateMockPurchaseRequestLineData()
        {
            return new List<PurchaseRequestLineDetailDto>
            {
                new()
                {
                    PQId = 1,
                    Company = 1,
                    QuantityQuoted = 100,
                    MPNQuoted = "TEST-PART-001",
                    ManufacturerName = "Test Manufacturer"
                }
            };
        }

        private List<ReportColumn> CreateMockReportColumns()
        {
            return new List<ReportColumn>
            {
                new()
                {
                    ReportColumnId = 1,
                    TitleResource = "TestColumn1",
                    ReportColumnFormatNo = (int)ReportColumnFormat.Text,
                    ReportNo = (int)Report.RequirementWithBOM
                },
                new()
                {
                    ReportColumnId = 2,
                    TitleResource = "TestColumn2",
                    ReportColumnFormatNo = (int)ReportColumnFormat.Currency,
                    ReportNo = (int)Report.RequirementWithBOM
                }
            };
        }

        private List<ReportColumn> CreateMockReportColumnsWithUnitPrice()
        {
            return new List<ReportColumn>
            {
                new()
                {
                    ReportColumnId = 1,
                    TitleResource = "UnitPrice",
                    ReportColumnFormatNo = (int)ReportColumnFormat.UnitPrice,
                    ReportNo = (int)Report.RequirementWithBOM
                }
            };
        }

        private void SetupMockRepositories(
            List<CustomerRequirementForBomDto>? bomData,
            List<PurchaseRequestLineDetailDto>? purchaseData,
            List<ReportColumn> columns)
        {
            if (bomData != null)
            {
                _mockRepository.Setup(x => x.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                    .ReturnsAsync(new List<CustomerRequirementForBom>());
                _mockMapper.Setup(x => x.Map<List<CustomerRequirementForBomDto>>(It.IsAny<object>()))
                    .Returns(bomData);
            }

            if (purchaseData != null)
            {
                _mockRepositoryPurchaseRequestLine.Setup(x => x.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                    .ReturnsAsync(new List<PurchaseRequestLineDetail>());
                _mockMapper.Setup(x => x.Map<List<PurchaseRequestLineDetailDto>>(It.IsAny<object>()))
                    .Returns(purchaseData);
            }

            _mockRepositoryReportColumn.Setup(x => x.ListAsync(
                It.IsAny<System.Linq.Expressions.Expression<Func<ReportColumn, bool>>>(),
                It.IsAny<Func<IQueryable<ReportColumn>, IOrderedQueryable<ReportColumn>>>(),
                It.IsAny<System.Linq.Expressions.Expression<Func<ReportColumn, object?>>>()))
                .ReturnsAsync(columns);
        }

        #region GetFormattedItem Tests

        // Helper method to invoke the private static GetFormattedItem method via reflection
        private static string InvokeGetFormattedItem(int formatNo, object data)
        {
            var method = typeof(CreateExportToCsvHandler).GetMethod("GetFormattedItem",
                BindingFlags.NonPublic | BindingFlags.Static);
            return (string)method!.Invoke(null, new object[] { formatNo, data })!;
        }

        [Fact]
        public void GetFormattedItem_WhenDataIsDBNull_ShouldReturnEmptyString()
        {
            // Arrange
            var data = DBNull.Value;
            var formatNo = (int)ReportColumnFormat.Text;

            // Act
            var result = InvokeGetFormattedItem(formatNo, data);

            // Assert
            result.Should().BeEmpty();
        }

        [Fact]
        public void GetFormattedItem_WhenDataIsNull_ShouldReturnEmptyString()
        {
            // Arrange
            object? data = null;
            var formatNo = (int)ReportColumnFormat.Text;

            // Act
            var result = InvokeGetFormattedItem(formatNo, data!);

            // Assert
            result.Should().BeEmpty();
        }

        [Fact]
        public void GetFormattedItem_WhenFormatIsDateTime_ShouldReturnFormattedDate()
        {
            // Arrange
            var data = new DateTime(2024, 1, 15);
            var formatNo = (int)ReportColumnFormat.DateTime;

            // Act
            var result = InvokeGetFormattedItem(formatNo, data);

            // Assert
            result.Should().Be("15/01/2024");
        }

        [Fact]
        public void GetFormattedItem_WhenFormatIsDateTimeWithTime_ShouldReturnFormattedDateWithTime()
        {
            // Arrange
            var data = new DateTime(2024, 1, 15, 14, 30, 0);
            var formatNo = (int)ReportColumnFormat.DateTimeWithTime;

            // Act
            var result = InvokeGetFormattedItem(formatNo, data);

            // Assert
            result.Should().Be("15/01/2024 14:30");
        }

        [Fact]
        public void GetFormattedItem_WhenFormatIsNumerical_ShouldReturnFormattedNumber()
        {
            // Arrange
            var data = 123.456m;
            var formatNo = (int)ReportColumnFormat.Numerical;

            // Act
            var result = InvokeGetFormattedItem(formatNo, data);

            // Assert
            result.Should().Be("123.46");
        }

        [Fact]
        public void GetFormattedItem_WhenFormatIsCurrency_ShouldReturnFormattedCurrencyWithCommas()
        {
            // Arrange
            var data = 1234.56m;
            var formatNo = (int)ReportColumnFormat.Currency;

            // Act
            var result = InvokeGetFormattedItem(formatNo, data);

            // Assert
            result.Should().Be("1,234.56");
        }

        [Fact]
        public void GetFormattedItem_WhenFormatIsUnitPrice_ShouldReturnFormattedPriceWithFiveDecimals()
        {
            // Arrange
            var data = 12.123456m;
            var formatNo = (int)ReportColumnFormat.UnitPrice;

            // Act
            var result = InvokeGetFormattedItem(formatNo, data);

            // Assert
            result.Should().Be("12.12346");
        }

        [Fact]
        public void GetFormattedItem_WhenFormatIsText_ShouldReturnCleanedText()
        {
            // Arrange
            var data = "Test–text—with–dashes";
            var formatNo = (int)ReportColumnFormat.Text;

            // Act
            var result = InvokeGetFormattedItem(formatNo, data);

            // Assert
            result.Should().Be("Test-text-with-dashes");
        }

        [Fact]
        public void GetFormattedItem_WhenFormatIsTextWithRegularString_ShouldReturnSameString()
        {
            // Arrange
            var data = "Regular text without special characters";
            var formatNo = (int)ReportColumnFormat.Text;

            // Act
            var result = InvokeGetFormattedItem(formatNo, data);

            // Assert
            result.Should().Be("Regular text without special characters");
        }

        [Fact]
        public void GetFormattedItem_WhenFormatIsInvalid_ShouldReturnEmptyString()
        {
            // Arrange
            var data = "test data";
            var formatNo = 999; // Invalid format number

            // Act
            var result = InvokeGetFormattedItem(formatNo, data);

            // Assert
            result.Should().BeEmpty();
        }

        [Fact]
        public void GetFormattedItem_WhenFormatIsZero_ShouldReturnEmptyString()
        {
            // Arrange
            var data = "test data";
            var formatNo = 0;

            // Act
            var result = InvokeGetFormattedItem(formatNo, data);

            // Assert
            result.Should().BeEmpty();
        }

        [Fact]
        public void GetFormattedItem_WhenDateTimeFromString_ShouldConvertAndFormat()
        {
            // Arrange
            var data = "2024-01-15T14:30:00";
            var formatNo = (int)ReportColumnFormat.DateTime;

            // Act
            var result = InvokeGetFormattedItem(formatNo, data);

            // Assert
            result.Should().Be("15/01/2024");
        }

        [Fact]
        public void GetFormattedItem_WhenDateTimeWithTimeFromString_ShouldConvertAndFormatWithTime()
        {
            // Arrange
            var data = "2024-01-15T14:30:00";
            var formatNo = (int)ReportColumnFormat.DateTimeWithTime;

            // Act
            var result = InvokeGetFormattedItem(formatNo, data);

            // Assert
            result.Should().Be("15/01/2024 14:30");
        }

        [Fact]
        public void GetFormattedItem_WhenNumericalWithInteger_ShouldFormatWithTwoDecimals()
        {
            // Arrange
            var data = 100;
            var formatNo = (int)ReportColumnFormat.Numerical;

            // Act
            var result = InvokeGetFormattedItem(formatNo, data);

            // Assert
            result.Should().Be("100.00");
        }

        [Fact]
        public void GetFormattedItem_WhenNumericalWithDouble_ShouldFormatWithTwoDecimals()
        {
            // Arrange
            var data = 123.789;
            var formatNo = (int)ReportColumnFormat.Numerical;

            // Act
            var result = InvokeGetFormattedItem(formatNo, data);

            // Assert
            result.Should().Be("123.79");
        }

        [Fact]
        public void GetFormattedItem_WhenCurrencyWithLargeNumber_ShouldFormatWithCommas()
        {
            // Arrange
            var data = 1234567.89m;
            var formatNo = (int)ReportColumnFormat.Currency;

            // Act
            var result = InvokeGetFormattedItem(formatNo, data);

            // Assert
            result.Should().Be("1,234,567.89");
        }

        [Fact]
        public void GetFormattedItem_WhenCurrencyWithNegativeNumber_ShouldFormatNegative()
        {
            // Arrange
            var data = -1234.56m;
            var formatNo = (int)ReportColumnFormat.Currency;

            // Act
            var result = InvokeGetFormattedItem(formatNo, data);

            // Assert
            result.Should().Be("-1,234.56");
        }

        [Fact]
        public void GetFormattedItem_WhenUnitPriceWithSmallDecimal_ShouldFormatWithFiveDecimals()
        {
            // Arrange
            var data = 0.00123m;
            var formatNo = (int)ReportColumnFormat.UnitPrice;

            // Act
            var result = InvokeGetFormattedItem(formatNo, data);

            // Assert
            result.Should().Be("0.00123");
        }

        [Fact]
        public void GetFormattedItem_WhenUnitPriceWithZero_ShouldFormatZeroWithFiveDecimals()
        {
            // Arrange
            var data = 0m;
            var formatNo = (int)ReportColumnFormat.UnitPrice;

            // Act
            var result = InvokeGetFormattedItem(formatNo, data);

            // Assert
            result.Should().Be("0.00000");
        }

        [Fact]
        public void GetFormattedItem_WhenTextWithEmptyString_ShouldReturnEmptyString()
        {
            // Arrange
            var data = "";
            var formatNo = (int)ReportColumnFormat.Text;

            // Act
            var result = InvokeGetFormattedItem(formatNo, data);

            // Assert
            result.Should().BeEmpty();
        }

        [Fact]
        public void GetFormattedItem_WhenTextWithWhitespace_ShouldReturnWhitespace()
        {
            // Arrange
            var data = "   ";
            var formatNo = (int)ReportColumnFormat.Text;

            // Act
            var result = InvokeGetFormattedItem(formatNo, data);

            // Assert
            result.Should().Be("   ");
        }

        [Theory]
        [InlineData(1, ReportColumnFormat.DateTime)]
        [InlineData(2, ReportColumnFormat.Numerical)]
        [InlineData(3, ReportColumnFormat.Currency)]
        [InlineData(4, ReportColumnFormat.Text)]
        [InlineData(5, ReportColumnFormat.UnitPrice)]
        [InlineData(6, ReportColumnFormat.DateTimeWithTime)]
        public void GetFormattedItem_WhenValidFormatNumbers_ShouldMatchEnumValues(int formatNo, ReportColumnFormat expectedEnum)
        {
            // This test verifies that the format numbers used in the method match the enum values
            // Arrange & Act & Assert
            ((int)expectedEnum).Should().Be(formatNo);
        }

        [Fact]
        public void GetFormattedItem_WhenDateTimeInvalidString_ShouldThrowException()
        {
            // Arrange
            var data = "invalid date string";
            var formatNo = (int)ReportColumnFormat.DateTime;

            // Act & Assert
            var exception = Assert.Throws<TargetInvocationException>(() => InvokeGetFormattedItem(formatNo, data));
            Assert.IsType<FormatException>(exception.InnerException);
        }

        [Fact]
        public void GetFormattedItem_WhenDateTimeWithTimeInvalidString_ShouldThrowException()
        {
            // Arrange
            var data = "not a date";
            var formatNo = (int)ReportColumnFormat.DateTimeWithTime;

            // Act & Assert
            var exception = Assert.Throws<TargetInvocationException>(() => InvokeGetFormattedItem(formatNo, data));
            Assert.IsType<FormatException>(exception.InnerException);
        }

        [Fact]
        public void GetFormattedItem_WhenNumericalWithStringNumber_ShouldConvertAndFormat()
        {
            // Arrange
            var data = "123.45";
            var formatNo = (int)ReportColumnFormat.Numerical;

            // Act
            var result = InvokeGetFormattedItem(formatNo, data);

            // Assert
            result.Should().Be("123.45");
        }

        [Fact]
        public void GetFormattedItem_WhenCurrencyWithStringNumber_ShouldConvertAndFormat()
        {
            // Arrange
            var data = "1234.56";
            var formatNo = (int)ReportColumnFormat.Currency;

            // Act
            var result = InvokeGetFormattedItem(formatNo, data);

            // Assert
            result.Should().Be("1,234.56");
        }

        [Fact]
        public void GetFormattedItem_WhenUnitPriceWithStringNumber_ShouldConvertAndFormat()
        {
            // Arrange
            var data = "12.123456";
            var formatNo = (int)ReportColumnFormat.UnitPrice;

            // Act
            var result = InvokeGetFormattedItem(formatNo, data);

            // Assert
            result.Should().Be("12.12346");
        }

        [Fact]
        public void GetFormattedItem_WhenTextWithSpecialCharacters_ShouldCleanJunkChars()
        {
            // Arrange
            var data = "Text–with—various–dash—types";
            var formatNo = (int)ReportColumnFormat.Text;

            // Act
            var result = InvokeGetFormattedItem(formatNo, data);

            // Assert
            result.Should().Be("Text-with-various-dash-types");
        }

        [Fact]
        public void GetFormattedItem_WhenTextWithNumbers_ShouldReturnAsIs()
        {
            // Arrange
            var data = "ABC123XYZ789";
            var formatNo = (int)ReportColumnFormat.Text;

            // Act
            var result = InvokeGetFormattedItem(formatNo, data);

            // Assert
            result.Should().Be("ABC123XYZ789");
        }

        [Fact]
        public void GetFormattedItem_WhenDateTimeMinValue_ShouldReturnEmptyString()
        {
            // Arrange
            var data = new DateTime(1, 1, 1);
            var formatNo = (int)ReportColumnFormat.DateTime;

            // Act
            var result = InvokeGetFormattedItem(formatNo, data);

            // Assert
            result.Should().BeEmpty();
        }

        [Fact]
        public void GetFormattedItem_WhenDateTimeWithTimeMinValue_ShouldReturnEmptyString()
        {
            // Arrange
            var data = new DateTime(1, 1, 1);
            var formatNo = (int)ReportColumnFormat.DateTimeWithTime;

            // Act
            var result = InvokeGetFormattedItem(formatNo, data);

            // Assert
            result.Should().BeEmpty();
        }

        #endregion
    }
}
