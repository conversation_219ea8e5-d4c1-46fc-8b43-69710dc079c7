using AutoFixture;
using AutoFixture.AutoMoq;
using AutoMapper;
using FluentAssertions;
using GlobalTrader2.Aggregator.UseCases.Orders.Requirements.ExportToCSV.Commands;
using GlobalTrader2.Aggregator.UseCases.Orders.Requirements.ExportToCSV.Commands.Dtos;
using GlobalTrader2.Core;
using GlobalTrader2.Core.Domain.Entities;
using GlobalTrader2.Core.Enums;
using Moq;
using System.Text;

namespace GlobalTrader2.Aggregator.Test.Orders.Requirements.ExportToCSV.Commands
{
    public class CreateExportToCSVHandlerTests
    {
        private readonly IFixture _fixture;
        private readonly Mock<IBaseRepository<CustomerRequirementForBom>> _mockRepository;
        private readonly Mock<IBaseRepository<PurchaseRequestLineDetail>> _mockRepositoryPurchaseRequestLine;
        private readonly Mock<IBaseRepository<ReportColumn>> _mockRepositoryReportColumn;
        private readonly Mock<IMapper> _mockMapper;
        private readonly CreateExportToCsvHandler _handler;

        public CreateExportToCSVHandlerTests()
        {
            _fixture = new Fixture().Customize(new AutoMoqCustomization());
            _fixture.Behaviors.Add(new OmitOnRecursionBehavior());

            _mockRepository = _fixture.Freeze<Mock<IBaseRepository<CustomerRequirementForBom>>>();
            _mockRepositoryPurchaseRequestLine = _fixture.Freeze<Mock<IBaseRepository<PurchaseRequestLineDetail>>>();
            _mockRepositoryReportColumn = _fixture.Freeze<Mock<IBaseRepository<ReportColumn>>>();
            _mockMapper = _fixture.Freeze<Mock<IMapper>>();

            _handler = new CreateExportToCsvHandler(
                _mockRepository.Object,
                _mockRepositoryReportColumn.Object,
                _mockRepositoryPurchaseRequestLine.Object,
                _mockMapper.Object);
        }

        [Fact]
        public async Task Handle_WhenRequestIsNull_ShouldThrowArgumentNullException()
        {
            // Arrange
            CreateExportToCsvCommand? request = null;

            // Act & Assert
            await Assert.ThrowsAsync<ArgumentNullException>(() =>
                _handler.Handle(request!, CancellationToken.None));
        }

        [Fact]
        public async Task Handle_WhenReportIsRequirementWithBOM_ShouldReturnSuccessWithCorrectFileName()
        {
            // Arrange
            var request = CreateTestCommand(Report.RequirementWithBOM, "N");
            var mockBomData = CreateMockBomData();
            var mockColumns = CreateMockReportColumns();

            SetupMockRepositories(mockBomData, null, mockColumns);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            result.Success.Should().BeTrue();
            result.Data.Should().NotBeNull();
            result.Data.FileName.Should().StartWith("PriceRequest_");
            result.Data.FileName.Should().EndWith(".csv");
            result.Data.File.Should().NotBeEmpty();
        }

        [Fact]
        public async Task Handle_WhenReportIsPurchaseQuote_ShouldReturnSuccessWithCorrectFileName()
        {
            // Arrange
            var request = CreateTestCommand(Report.PurchaseQuote, "N");
            var mockPurchaseData = CreateMockPurchaseRequestLineData();
            var mockColumns = CreateMockReportColumns();

            SetupMockRepositories(null, mockPurchaseData, mockColumns);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            result.Success.Should().BeTrue();
            result.Data.Should().NotBeNull();
            result.Data.FileName.Should().StartWith("PriceRequest_");
            result.Data.FileName.Should().EndWith(".csv");
            result.Data.File.Should().NotBeEmpty();
        }

        [Fact]
        public async Task Handle_WhenExportIsE_ShouldGenerateFileNameWithE()
        {
            // Arrange
            var request = CreateTestCommand(Report.RequirementWithBOM, "E");
            var mockBomData = CreateMockBomData();
            var mockColumns = CreateMockReportColumns();

            SetupMockRepositories(mockBomData, null, mockColumns);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            result.Success.Should().BeTrue();
            result.Data.FileName.Should().StartWith("PriceRequestE_");
        }

        [Fact]
        public async Task Handle_WhenExportIsNotE_ShouldGenerateFileNameWithoutE()
        {
            // Arrange
            var request = CreateTestCommand(Report.RequirementWithBOM, "N");
            var mockBomData = CreateMockBomData();
            var mockColumns = CreateMockReportColumns();

            SetupMockRepositories(mockBomData, null, mockColumns);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            result.Success.Should().BeTrue();
            result.Data.FileName.Should().StartWith("PriceRequest_");
            result.Data.FileName.Should().NotContain("PriceRequestE_");
        }

        [Fact]
        public async Task Handle_WhenDataExists_ShouldGenerateCSVWithCorrectFormat()
        {
            // Arrange
            var request = CreateTestCommand(Report.RequirementWithBOM, "N");
            var mockBomData = CreateMockBomData();
            var mockColumns = CreateMockReportColumns();

            SetupMockRepositories(mockBomData, null, mockColumns);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            result.Success.Should().BeTrue();
            var csvContent = Encoding.UTF8.GetString(result.Data.File);
            csvContent.Should().NotBeEmpty();
            csvContent.Should().Contain("RequirementWithBOM"); // Report title
            csvContent.Should().Contain("Abbreviation->"); // Abbreviation line
        }

        [Fact]
        public void ConverListObject_WhenSourceListIsNull_ShouldReturnEmptyList()
        {
            // Arrange
            List<CustomerRequirementForBomDto>? sourceList = null;

            // Act
            var result = CreateExportToCsvHandler.ConverListObject(sourceList!);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Fact]
        public void ConverListObject_WhenSourceListIsEmpty_ShouldReturnEmptyList()
        {
            // Arrange
            var sourceList = new List<CustomerRequirementForBomDto>();

            // Act
            var result = CreateExportToCsvHandler.ConverListObject(sourceList);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Fact]
        public void ConverListObject_WhenSourceListHasData_ShouldConvertToObjectList()
        {
            // Arrange
            var sourceList = new List<CustomerRequirementForBomDto>
            {
                new() { RequirementNumber = "REQ001", Company = 1, QuantityQuoted = 100 },
                new() { RequirementNumber = "REQ002", Company = 2, QuantityQuoted = 200 }
            };

            // Act
            var result = CreateExportToCsvHandler.ConverListObject(sourceList);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            result[0].Should().NotBeEmpty();
            result[1].Should().NotBeEmpty();
        }

        [Fact]
        public void ConverListObject_WhenPropertyIsNull_ShouldUseEmptyBraces()
        {
            // Arrange
            var sourceList = new List<CustomerRequirementForBomDto>
            {
                new() { RequirementNumber = null, Company = 1, QuantityQuoted = 100 }
            };

            // Act
            var result = CreateExportToCsvHandler.ConverListObject(sourceList);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(1);
            result[0].Should().Contain("{}");
        }

        [Fact]
        public async Task Handle_WhenDataHasMoreThan13Columns_ShouldIncludeNotesInHeadings()
        {
            // Arrange
            var request = CreateTestCommand(Report.RequirementWithBOM, "N");
            var mockBomData = CreateMockBomDataWithManyColumns();
            var mockColumns = CreateMockReportColumns();

            SetupMockRepositories(mockBomData, null, mockColumns);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            result.Success.Should().BeTrue();
            var csvContent = Encoding.UTF8.GetString(result.Data.File);
            csvContent.Should().Contain("Notes:");
        }

        [Fact]
        public async Task Handle_WhenPurchaseQuoteWithExportE_ShouldGenerateCorrectFileName()
        {
            // Arrange
            var request = CreateTestCommand(Report.PurchaseQuote, "E");
            var mockPurchaseData = CreateMockPurchaseRequestLineData();
            var mockColumns = CreateMockReportColumns();

            SetupMockRepositories(null, mockPurchaseData, mockColumns);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            result.Success.Should().BeTrue();
            result.Data.FileName.Should().StartWith("PriceRequestE_");
            result.Data.FileName.Should().EndWith(".csv");
        }

        [Fact]
        public async Task Handle_WhenResourcesAreEmpty_ShouldUseResourceKeys()
        {
            // Arrange
            var request = CreateTestCommand(Report.RequirementWithBOM, "N");
            request.Resources = new List<(string key, string value)>(); // Empty resources
            var mockBomData = CreateMockBomData();
            var mockColumns = CreateMockReportColumns();

            SetupMockRepositories(mockBomData, null, mockColumns);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            result.Success.Should().BeTrue();
            var csvContent = Encoding.UTF8.GetString(result.Data.File);
            csvContent.Should().NotBeEmpty();
        }

        [Fact]
        public async Task Handle_WhenRepositoryThrowsException_ShouldPropagateException()
        {
            // Arrange
            var request = CreateTestCommand(Report.RequirementWithBOM, "N");

            _mockRepository.Setup(x => x.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                .ThrowsAsync(new InvalidOperationException("Database error"));

            // Act & Assert
            await Assert.ThrowsAsync<InvalidOperationException>(() =>
                _handler.Handle(request, CancellationToken.None));
        }

        [Fact]
        public async Task Handle_WhenMapperThrowsException_ShouldPropagateException()
        {
            // Arrange
            var request = CreateTestCommand(Report.RequirementWithBOM, "N");

            _mockRepository.Setup(x => x.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                .ReturnsAsync(new List<CustomerRequirementForBom>());
            _mockMapper.Setup(x => x.Map<List<CustomerRequirementForBomDto>>(It.IsAny<object>()))
                .Throws(new AutoMapperMappingException("Mapping error"));

            // Act & Assert
            await Assert.ThrowsAsync<AutoMapperMappingException>(() =>
                _handler.Handle(request, CancellationToken.None));
        }

        // Helper methods for creating test data
        private CreateExportToCsvCommand CreateTestCommand(Report report, string export)
        {
            return new CreateExportToCsvCommand
            {
                Report = report,
                Id = 123,
                CurrencyCode = "USD",
                Export = export,
                ClientID = 456,
                LoginFullName = "Test User",
                Resources = new List<(string key, string value)>
                {
                    ("ReportTitlesRequirementWithBOM", "RequirementWithBOM"),
                    ("ReportTitlesPurchaseQuote", "PurchaseQuote"),
                    ("MiscAppTitle", "GlobalTrader"),
                    ("ReportsDateAndLogin", "Date: {0}, Login: {1}"),
                    ("MiscNotes", "Notes: "),
                    ("NotFoundReportData", "No data found"),
                    ("ReportsTestColumn1", "TestColumn1"),
                    ("ReportsTestColumn2", "TestColumn2"),
                    ("ReportsUnitPrice", "UnitPrice")
                }
            };
        }

        private List<CustomerRequirementForBomDto> CreateMockBomData()
        {
            return new List<CustomerRequirementForBomDto>
            {
                new()
                {
                    RequirementNumber = "REQ001",
                    Company = 1,
                    QuantityQuoted = 100,
                    MPNQuoted = "TEST-PART-001",
                    ManufacturerName = "Test Manufacturer",
                    UnitPrice = 10.50m
                }
            };
        }

        private List<CustomerRequirementForBomDto> CreateMultipleMockBomData()
        {
            return new List<CustomerRequirementForBomDto>
            {
                new()
                {
                    RequirementNumber = "REQ001",
                    Company = 1,
                    QuantityQuoted = 100,
                    MPNQuoted = "TEST-PART-001",
                    ManufacturerName = "Test Manufacturer",
                    UnitPrice = 10.50m
                },
                new()
                {
                    RequirementNumber = "REQ002",
                    Company = 2,
                    QuantityQuoted = 200,
                    MPNQuoted = "TEST-PART-002",
                    ManufacturerName = "Test Manufacturer 2",
                    UnitPrice = 20.75m
                }
            };
        }

        private List<CustomerRequirementForBomDto> CreateMockBomDataWithManyColumns()
        {
            return new List<CustomerRequirementForBomDto>
            {
                new()
                {
                    RequirementNumber = "REQ001",
                    Company = 1,
                    QuantityQuoted = 100,
                    MPNQuoted = "TEST-PART-001",
                    ManufacturerName = "Test Manufacturer",
                    DateCode = "2024",
                    PackageType = "SOIC",
                    ProductType = "IC",
                    SPQ = "1000",
                    MOQ = "100",
                    LeadTimeWks = "12",
                    RohsYN = "Y",
                    TQSA = "5000",
                    LTB = "N",
                    FactorySealed = "Y",
                    MSL = "3",
                    UnitPrice = 10.50m,
                    SupplierNotes = "Test notes for column 15+"
                }
            };
        }

        private List<PurchaseRequestLineDetailDto> CreateMockPurchaseRequestLineData()
        {
            return new List<PurchaseRequestLineDetailDto>
            {
                new()
                {
                    PQId = 1,
                    Company = 1,
                    QuantityQuoted = 100,
                    MPNQuoted = "TEST-PART-001",
                    ManufacturerName = "Test Manufacturer"
                }
            };
        }

        private List<ReportColumn> CreateMockReportColumns()
        {
            return new List<ReportColumn>
            {
                new()
                {
                    ReportColumnId = 1,
                    TitleResource = "TestColumn1",
                    ReportColumnFormatNo = (int)ReportColumnFormat.Text,
                    ReportNo = (int)Report.RequirementWithBOM
                },
                new()
                {
                    ReportColumnId = 2,
                    TitleResource = "TestColumn2",
                    ReportColumnFormatNo = (int)ReportColumnFormat.Currency,
                    ReportNo = (int)Report.RequirementWithBOM
                }
            };
        }

        private List<ReportColumn> CreateMockReportColumnsWithUnitPrice()
        {
            return new List<ReportColumn>
            {
                new()
                {
                    ReportColumnId = 1,
                    TitleResource = "UnitPrice",
                    ReportColumnFormatNo = (int)ReportColumnFormat.UnitPrice,
                    ReportNo = (int)Report.RequirementWithBOM
                }
            };
        }

        private void SetupMockRepositories(
            List<CustomerRequirementForBomDto>? bomData,
            List<PurchaseRequestLineDetailDto>? purchaseData,
            List<ReportColumn> columns)
        {
            if (bomData != null)
            {
                _mockRepository.Setup(x => x.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                    .ReturnsAsync(new List<CustomerRequirementForBom>());
                _mockMapper.Setup(x => x.Map<List<CustomerRequirementForBomDto>>(It.IsAny<object>()))
                    .Returns(bomData);
            }

            if (purchaseData != null)
            {
                _mockRepositoryPurchaseRequestLine.Setup(x => x.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                    .ReturnsAsync(new List<PurchaseRequestLineDetail>());
                _mockMapper.Setup(x => x.Map<List<PurchaseRequestLineDetailDto>>(It.IsAny<object>()))
                    .Returns(purchaseData);
            }

            _mockRepositoryReportColumn.Setup(x => x.ListAsync(
                It.IsAny<System.Linq.Expressions.Expression<Func<ReportColumn, bool>>>(),
                It.IsAny<Func<IQueryable<ReportColumn>, IOrderedQueryable<ReportColumn>>>(),
                It.IsAny<System.Linq.Expressions.Expression<Func<ReportColumn, object?>>>()))
                .ReturnsAsync(columns);
        }
    }
}
